// Main JavaScript file for Barber Brotherz website
console.log('Main script loaded');

// Wait for the DOM to fully load
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing features...');

    // Initialize all features
    initNavbarScroll();
    initSmoothScrolling();
    initAppointmentForm();
    initActiveNavigation();
    initWorkCarousel();
    initLazyLoading();
});

// Function to handle navbar color change on scroll
function initNavbarScroll() {
    const navbar = document.querySelector('.navbar');
    if (!navbar) return;
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            navbar.style.backgroundColor = '#000000';
            navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.3)';
        } else {
            navbar.style.backgroundColor = 'transparent';
            navbar.style.boxShadow = 'none';
        }
    });
    console.log('Navbar scroll effect initialized');
}

// Function to initialize the appointment form
function initAppointmentForm() {
    console.log('Initializing appointment form...');
    
    // Set minimum date for appointment
    const dateInput = document.getElementById('date');
    if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;
        console.log('Date input minimum set to:', today);
    }

    // Get the form element
    const form = document.getElementById('appointmentForm');
    if (!form) {
        console.error('Appointment form not found!');
        return;
    }

    // Handle form submission
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        console.log('Form submission started');

        // Get form data
        const formData = {
            name: document.getElementById('name').value.trim(),
            phone: document.getElementById('phone').value.trim(),
            service: document.getElementById('service').value,
            date: document.getElementById('date').value,
            time: document.getElementById('time').value,
            notes: document.getElementById('notes') ? document.getElementById('notes').value.trim() : ''
        };

        // Validate form data
        if (!formData.name || !formData.phone || !formData.service || !formData.date || !formData.time) {
            showMessage('Please fill in all required fields', 'error');
            return;
        }

        try {
            // Format phone number
            const formattedPhone = formatPhoneNumber(formData.phone);
            console.log('Formatted phone:', formattedPhone);

            // Send to server
            const response = await fetch('http://localhost:3000/send-sms-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    ...formData,
                    phone: formattedPhone
                })
            });

            const data = await response.json();
            console.log('Server response:', data);

            if (response.ok) {
                showMessage(`Thank you, ${formData.name}! Your appointment has been booked successfully.`, 'success');
                form.reset();
            } else {
                throw new Error(data.message || 'Failed to book appointment');
            }
        } catch (error) {
            console.error('Booking error:', error);
            showMessage('Something went wrong. Please try again or call us directly at (*************', 'error');
        }
    });

    console.log('Appointment form initialized successfully');
}

// Function to format phone numbers
function formatPhoneNumber(phone) {
    if (!phone) return '';
    
    // Remove all non-digits
    const digits = phone.replace(/\D/g, '');
    
    // Handle different formats
    if (phone.startsWith('+1') && phone.length >= 12) {
        return phone; // Already in E.164
    } else if (digits.length === 10) {
        return `+1${digits}`;
    } else if (digits.length === 11 && digits.startsWith('1')) {
        return `+${digits}`;
    }
    
    console.warn('Invalid phone format:', phone);
    return phone;
}

// Function to show messages (success/error)
function showMessage(message, type = 'success') {
    // Remove any existing messages
    const existingMessages = document.querySelectorAll('.alert');
    existingMessages.forEach(msg => msg.remove());

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} mt-3`;
    messageDiv.innerHTML = `
        <h4>${type === 'success' ? 'Success!' : 'Oops! Something went wrong.'}</h4>
        <p>${message}</p>
    `;

    // Add to form
    const form = document.getElementById('appointmentForm');
    if (form) {
        form.appendChild(messageDiv);
        messageDiv.scrollIntoView({ behavior: 'smooth' });
    }
}

// Function to handle smooth scrolling
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            
            if (target) {
                // Close mobile menu if open
                const navbarToggler = document.querySelector('.navbar-toggler');
                const navbarCollapse = document.querySelector('.navbar-collapse');
                if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                    navbarToggler.click();
                }

                // Scroll to target
                window.scrollTo({
                    top: target.offsetTop - 70,
                    behavior: 'smooth'
                });
            }
        });
    });
    console.log('Smooth scrolling initialized');
}

// Function to handle active navigation
function initActiveNavigation() {
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('section');
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            if (window.scrollY >= (sectionTop - 100)) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    console.log('Active navigation initialized');
}

// Function to initialize the work carousel
function initWorkCarousel() {
    // Array of all unused images for the carousel
    const carouselImages = [
        'images/a1.JPG',
        'images/a2.JPG',
        'images/a3.JPG',
        'images/a4.JPG',
        'images/a5.JPG',
        'images/a6.PNG',
        'images/IMG_0198.JPG',
        'images/IMG_0338_Original.JPG',
        'images/IMG_0339_Original.JPG',
        'images/IMG_0992.jpeg',
        'images/IMG_1330_Original.JPG',
        'images/IMG_1462_Original.JPG',
        'images/IMG_1463_Original.JPG',
        'images/IMG_1466_Original.JPG',
        'images/IMG_1555_Original 2.JPG',
        'images/IMG_6947_Original.JPG',
        'images/d2.jpeg',
        'images/d6.jpeg',
        'images/d7.jpeg',
        'images/d8.jpeg',
        'images/4BC1DDEE-4E25-4B9B-929E-762E4AC7AEEC.PNG'
    ];

    const carouselImage = document.getElementById('carousel-image');
    const currentImageSpan = document.getElementById('current-image');
    const totalImagesSpan = document.getElementById('total-images');

    if (!carouselImage || !currentImageSpan || !totalImagesSpan) {
        console.log('Carousel elements not found, skipping initialization');
        return;
    }

    let currentIndex = 0;
    let isTransitioning = false;

    // Set total images count
    totalImagesSpan.textContent = carouselImages.length;

    // Function to change image with fade effect
    function changeImage() {
        if (isTransitioning) return;

        isTransitioning = true;

        // Add fade-out class
        carouselImage.classList.add('fade-out');

        setTimeout(() => {
            // Change to next image
            currentIndex = (currentIndex + 1) % carouselImages.length;
            carouselImage.src = carouselImages[currentIndex];

            // Update counter
            currentImageSpan.textContent = currentIndex + 1;

            // Remove fade-out and add fade-in
            carouselImage.classList.remove('fade-out');
            carouselImage.classList.add('fade-in');

            setTimeout(() => {
                carouselImage.classList.remove('fade-in');
                isTransitioning = false;
            }, 100);

        }, 400); // Half of the transition duration
    }

    // Start the carousel with automatic rotation every 4 seconds
    const carouselInterval = setInterval(changeImage, 4000);

    // Pause carousel on hover
    const carouselCard = document.querySelector('.work-carousel-card');
    if (carouselCard) {
        carouselCard.addEventListener('mouseenter', () => {
            clearInterval(carouselInterval);
        });

        carouselCard.addEventListener('mouseleave', () => {
            // Restart the interval when mouse leaves
            setTimeout(() => {
                setInterval(changeImage, 4000);
            }, 1000);
        });
    }

    // Preload images for smoother transitions
    carouselImages.forEach((imageSrc, index) => {
        if (index > 0) { // Skip first image as it's already loaded
            const img = new Image();
            img.src = imageSrc;
        }
    });

    console.log('Work carousel initialized with', carouselImages.length, 'images');
}

// Function to initialize lazy loading for gallery images
function initLazyLoading() {
    // Check if Intersection Observer is supported
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    loadImage(img);
                    observer.unobserve(img);
                }
            });
        }, {
            // Start loading when image is 50px away from viewport
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all lazy images
        const lazyImages = document.querySelectorAll('.gallery-lazy-image');
        lazyImages.forEach(img => {
            imageObserver.observe(img);
        });

        console.log('Lazy loading initialized for', lazyImages.length, 'gallery images');
    } else {
        // Fallback for browsers without Intersection Observer
        console.log('Intersection Observer not supported, loading all images immediately');
        const lazyImages = document.querySelectorAll('.gallery-lazy-image');
        lazyImages.forEach(img => {
            loadImage(img);
        });
    }
}

// Function to load individual image with performance optimizations
function loadImage(img) {
    const dataSrc = img.getAttribute('data-src');
    if (!dataSrc) return;

    // Add loading class for visual feedback
    img.classList.add('loading');

    // Create a new image object for preloading
    const imageLoader = new Image();

    // Set up load event handler
    imageLoader.onload = function() {
        // Image loaded successfully
        img.src = dataSrc;
        img.classList.remove('loading');
        img.classList.add('loaded');

        // Remove data-src to prevent reloading
        img.removeAttribute('data-src');

        console.log('Image loaded successfully:', dataSrc);
    };

    // Set up error event handler
    imageLoader.onerror = function() {
        console.error('Failed to load image:', dataSrc);
        img.classList.remove('loading');
        img.alt = 'Image failed to load';

        // Set a fallback image or keep placeholder
        img.style.backgroundColor = '#f8f9fa';
        img.style.color = '#666';
    };

    // Start loading the image
    imageLoader.src = dataSrc;
}
